package com.example.splitexpenses.ui.components

import androidx.compose.animation.animateColorAsState
import androidx.compose.animation.animateContentSize
import androidx.compose.animation.core.FastOutSlowInEasing
import androidx.compose.animation.core.animateFloatAsState

import androidx.compose.animation.core.tween
import androidx.compose.foundation.BorderStroke

import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll

import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack

import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.LinearProgressIndicator
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.DividerDefaults
import androidx.compose.material3.FilterChip
import androidx.compose.material3.FilterChipDefaults
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight

import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.example.splitexpenses.R
import com.example.splitexpenses.ui.viewmodels.BalanceDetailsViewModel
import kotlin.math.abs

/**
 * Screen that displays detailed balance information for a group
 *
 * Features:
 * - Filter debts by specific users
 * - Real-time updates from Firebase
 * - Consistent UI with the rest of the application
 * - Organized layout for balances
 * - Loading and error states
 */
@OptIn(ExperimentalLayoutApi::class)
@Composable
fun BalanceDetailsScreen(
    viewModel: BalanceDetailsViewModel = hiltViewModel(),
    onBackClick: () -> Unit,
    onPreviousClick: (() -> Unit)? = null,
    onNextClick: (() -> Unit)? = null,
    hasPrevious: Boolean = false,
    hasNext: Boolean = false
) {
    // Function to get member avatar
    fun getMemberAvatar(memberName: String): String? {
        val avatar = viewModel.getMemberAvatar(memberName)
        println("BalanceDetailsScreen: Avatar for $memberName = $avatar, currentGroup = ${viewModel.currentGroup.value?.memberAvatars}")
        return avatar
    }
    val uiState by viewModel.uiState.collectAsState()
    val currentGroup by viewModel.currentGroup.collectAsState()

    // Get filtered data
    val filteredFinances = viewModel.getFilteredFinances()
    val filteredSettlements = viewModel.getFilteredSettlements()

    // Sort user balances by absolute balance value (highest first)
    val userBalances = filteredFinances.sortedByDescending { abs(it.userBalance) }

    Box(modifier = Modifier.fillMaxSize()) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .verticalScroll(rememberScrollState())
                .padding(bottom = if (onPreviousClick != null || onNextClick != null) 80.dp else 0.dp)
        ) {
            // Header with back button - matching the style of other screens
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                IconButton(onClick = onBackClick) {
                    Icon(Icons.AutoMirrored.Filled.ArrowBack, contentDescription = "Back")
                }

                Text(
                    text = "Balances",
                    style = MaterialTheme.typography.displayMedium,
                    color = MaterialTheme.colorScheme.primary
                )

                // Empty spacer to balance the back button
                Spacer(modifier = Modifier.width(48.dp))
            }

            // Error state
            if (uiState.error != null) {
                Card(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp),
                    colors = CardDefaults.cardColors(
                        containerColor = MaterialTheme.colorScheme.errorContainer
                    )
                ) {
                    Column(
                        modifier = Modifier.padding(16.dp),
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        Text(
                            text = "Error",
                            style = MaterialTheme.typography.titleMedium,
                            color = MaterialTheme.colorScheme.onErrorContainer
                        )
                        Spacer(modifier = Modifier.height(8.dp))
                        Text(
                            text = uiState.error ?: "Unknown error occurred",
                            style = MaterialTheme.typography.bodyMedium,
                            color = MaterialTheme.colorScheme.onErrorContainer
                        )
                        Spacer(modifier = Modifier.height(8.dp))
                        TextButton(
                            onClick = { viewModel.calculateFinances() }
                        ) {
                            Text("Retry")
                        }
                    }
                }
                return@Column
            }

            // Loading state
            if (uiState.isLoading) {
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(32.dp),
                    contentAlignment = Alignment.Center
                ) {
                    CircularProgressIndicator()
                }
                return@Column
            }

            // User filter section - matching the style of other screens
            Surface(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 16.dp, vertical = 8.dp),
                shape = MaterialTheme.shapes.medium,
                color = MaterialTheme.colorScheme.surface,
                border = BorderStroke(
                    width = 1.dp,
                    color = MaterialTheme.colorScheme.secondaryContainer
                ),
                shadowElevation = 2.dp
            ) {
                Column(
                    modifier = Modifier.padding(top=4.dp,bottom = 8.dp,start = 16.dp,end = 16.dp)
                ) {
                    // Filter header
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        // Left side - Filter icon and text
                        Row(
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Icon(
                                painter = if(uiState.selectedUserFilters.isNotEmpty())
                                    painterResource(id = R.drawable.filter)
                                else
                                    painterResource(id = R.drawable.filter_outline),
                                contentDescription = "Filter",
                                tint = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                            Spacer(modifier = Modifier.width(8.dp))
                            Text(
                                text = "Filter by Member",
                                style = MaterialTheme.typography.titleMedium,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }

                        // Right side - Clear button with fixed width
                        Box(
                            modifier = Modifier.width(72.dp), // Fixed width for the button area
                            contentAlignment = Alignment.CenterEnd
                        ) {
                            // Only show the button when filters are active
                            if (uiState.selectedUserFilters.isNotEmpty()) {
                                TextButton(
                                    onClick = { viewModel.clearUserFilters() },
                                    contentPadding = PaddingValues(horizontal = 8.dp)
                                ) {
                                    Text("Clear")
                                }
                            }else{
                                TextButton(
                                    onClick = {},
                                    contentPadding = PaddingValues(horizontal = 8.dp)
                                ) {
                                    Text("")
                                }
                            }
                        }
                    }

                    Spacer(modifier = Modifier.height(0.dp))

                    // User filter chips with horizontal scrolling
                    LazyRow(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.spacedBy(8.dp),
                        contentPadding = PaddingValues(horizontal = 4.dp)
                    ) {
                        items(currentGroup?.members?.toList() ?: emptyList()) { member ->
                            val isSelected = member in uiState.selectedUserFilters
                            val chipColor = animateColorAsState(
                                targetValue = if (isSelected)
                                    MaterialTheme.colorScheme.secondaryContainer
                                else
                                    MaterialTheme.colorScheme.surface,
                                animationSpec = tween(
                                    durationMillis = 300,
                                    easing = FastOutSlowInEasing
                                ),
                                label = "chipColor_$member"
                            )

                            FilterChip(
                                selected = isSelected,
                                onClick = { viewModel.toggleUserFilter(member) },
                                label = { Text(member) },
                                border = FilterChipDefaults.filterChipBorder(
                                    selected = isSelected,
                                    enabled = true,
                                    borderColor = MaterialTheme.colorScheme.secondaryContainer
                                ),
                                colors = FilterChipDefaults.filterChipColors(
                                    selectedContainerColor = chipColor.value
                                )
                            )
                        }
                    }
                }
            }

            Spacer(modifier = Modifier.height(8.dp))



            // User Balances Section
            Surface(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 16.dp, vertical = 8.dp),
                shape = MaterialTheme.shapes.medium,
                color = MaterialTheme.colorScheme.surface,
                border = BorderStroke(
                    width = 1.dp,
                    color = MaterialTheme.colorScheme.secondaryContainer
                ),
                shadowElevation = 2.dp
            ) {
                Column(
                    modifier = Modifier
                        .animateContentSize()
                        .padding(16.dp)
                ) {
                    Text(
                        text = "User Balances",
                        style = MaterialTheme.typography.titleMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )

                    Spacer(modifier = Modifier.height(8.dp))

                    if (userBalances.isEmpty()) {
                        Box(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(16.dp),
                            contentAlignment = Alignment.Center
                        ) {
                            Text(
                                text = if (uiState.selectedUserFilters.isEmpty())
                                    "No balances available"
                                else
                                    "No balances for selected members",
                                style = MaterialTheme.typography.bodyMedium,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                    } else {
                        // Calculate max balance for progress indicators
                        val maxBalance = userBalances.maxOfOrNull { abs(it.userBalance) } ?: 1.0

                        // User balance cards
                        userBalances.forEachIndexed { index, userFinance ->
                            val isCreditor = userFinance.userBalance > 0
                            val isDebtor = userFinance.userBalance < 0

                            Surface(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(vertical = 2.dp)
                                    .clickable { viewModel.toggleUserFilter(userFinance.userId) },
                                shape = MaterialTheme.shapes.medium,
                            ) {
                                Column(
                                    modifier = Modifier.padding(16.dp)
                                ) {
                                    Row(
                                        modifier = Modifier.fillMaxWidth(),
                                        horizontalArrangement = Arrangement.SpaceBetween,
                                        verticalAlignment = Alignment.CenterVertically
                                    ) {
                                        Row(
                                            verticalAlignment = Alignment.CenterVertically
                                        ) {
                                            // Avatar for user
                                            val userAvatar = getMemberAvatar(userFinance.userId)
                                            if (userAvatar != null) {
                                                Text(
                                                    text = userAvatar,
                                                    style = MaterialTheme.typography.displaySmall,
                                                    modifier = Modifier.padding(end = 8.dp)
                                                )
                                            } else {
                                                Icon(
                                                    painter = painterResource(id = R.drawable.account_outline),
                                                    contentDescription = null,
                                                    modifier = Modifier.size(38.dp).padding(end = 4.dp),
                                                    tint = MaterialTheme.colorScheme.onSurfaceVariant
                                                )
                                            }

                                            Column {
                                                Text(
                                                    text = userFinance.userId,
                                                    style = MaterialTheme.typography.titleMedium
                                                )
                                                Text(
                                                    text = "Spent: ${String.format("%.2f", userFinance.userExpense)}€",
                                                    style = MaterialTheme.typography.bodyMedium,
                                                    color = MaterialTheme.colorScheme.onSurfaceVariant
                                                )
                                            }
                                        }

                                        Column(
                                            horizontalAlignment = Alignment.End
                                        ) {
                                            Text(
                                                text = when {
                                                    isCreditor -> "Gets back"
                                                    isDebtor -> "Owes"
                                                    else -> "Balanced"
                                                },
                                                style = MaterialTheme.typography.bodySmall,
                                                color = MaterialTheme.colorScheme.onSurfaceVariant
                                            )
                                            // Animate the balance amount
                                            val animatedBalance = animateFloatAsState(
                                                targetValue = abs(userFinance.userBalance).toFloat(),
                                                animationSpec = tween(
                                                    durationMillis = 800,
                                                    easing = FastOutSlowInEasing
                                                ),
                                                label = "balanceAmount"
                                            )

                                            // Animate the text color
                                            val textColor = animateColorAsState(
                                                targetValue = when {
                                                    isCreditor -> MaterialTheme.colorScheme.primary
                                                    isDebtor -> MaterialTheme.colorScheme.error
                                                    else -> MaterialTheme.colorScheme.onSurface
                                                },
                                                animationSpec = tween(
                                                    durationMillis = 300,
                                                    easing = FastOutSlowInEasing
                                                ),
                                                label = "balanceTextColor"
                                            )

                                            Text(
                                                text = "${String.format("%.2f", animatedBalance.value)}€",
                                                style = MaterialTheme.typography.titleMedium,
                                                color = textColor.value,
                                                fontWeight = FontWeight.Bold
                                            )
                                        }
                                    }

//                                    // Progress indicator for balance amount
//                                    if (abs(userFinance.userBalance) > 0.01) {
//                                        Spacer(modifier = Modifier.height(8.dp))
//
//                                        val progress = (abs(userFinance.userBalance) / maxBalance).toFloat()
//                                        val animatedProgress = animateFloatAsState(
//                                            targetValue = progress,
//                                            animationSpec = tween(
//                                                durationMillis = 1000,
//                                                easing = FastOutSlowInEasing
//                                            ),
//                                            label = "balanceProgress_${userFinance.userId}"
//                                        )
//
//                                        LinearProgressIndicator(
//                                            progress = { animatedProgress.value },
//                                            modifier = Modifier
//                                                .fillMaxWidth()
//                                                .height(4.dp),
//                                            color = when {
//                                                isCreditor -> MaterialTheme.colorScheme.primary
//                                                isDebtor -> MaterialTheme.colorScheme.error
//                                                else -> MaterialTheme.colorScheme.onSurfaceVariant
//                                            },
//                                            trackColor = MaterialTheme.colorScheme.surfaceVariant,
//                                        )
//                                    }
                                }
                            }
                            if (index < userBalances.lastIndex) {
                                HorizontalDivider(
                                    modifier = Modifier,
                                    thickness = 1.dp,
                                    color = MaterialTheme.colorScheme.secondaryContainer
                                )
                            }
                        }
                    }
                }
            }

            Spacer(modifier = Modifier.height(8.dp))

            // Settlements Section
            Surface(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 16.dp, vertical = 8.dp),
                shape = MaterialTheme.shapes.medium,
                color = MaterialTheme.colorScheme.surface,
                border = BorderStroke(
                    width = 1.dp,
                    color = MaterialTheme.colorScheme.secondaryContainer
                ),
                shadowElevation = 2.dp
            ) {
                Column(
                    modifier = Modifier
                        .animateContentSize()
                        .padding(16.dp)
                ) {
                    Text(
                        text = "Settlements",
                        style = MaterialTheme.typography.titleMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )

                    Spacer(modifier = Modifier.height(8.dp))

                    if (filteredSettlements.isEmpty()) {
                        Box(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(16.dp),
                            contentAlignment = Alignment.Center
                        ) {
                            Text(
                                text = if (uiState.settlements.isEmpty())
                                    "All balances are settled!"
                                else if (uiState.selectedUserFilters.isEmpty())
                                    "No settlements needed"
                                else
                                    "No settlements for selected members",
                                style = MaterialTheme.typography.bodyMedium,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                    } else {
                        // Settlement cards
                        filteredSettlements.forEachIndexed { index, (debtorId, creditorId, amount) ->
                            val debtorSelected = debtorId in uiState.selectedUserFilters
                            val creditorSelected = creditorId in uiState.selectedUserFilters

                            // Determine if we have a single member filter applied
                            val singleMemberFilter = uiState.selectedUserFilters.size == 1
                            val filteredMemberId = if (singleMemberFilter) uiState.selectedUserFilters.first() else null

                            // When filtering by a single member, arrange so filtered member is always on left
                            val (leftMemberId, rightMemberId, filteredMemberOwes) = if (singleMemberFilter && filteredMemberId != null) {
                                when (filteredMemberId) {
                                    debtorId -> Triple(debtorId, creditorId, true)  // Filtered member owes money
                                    creditorId -> Triple(creditorId, debtorId, false) // Filtered member is owed money
                                    else -> Triple(debtorId, creditorId, null) // Shouldn't happen with proper filtering
                                }
                            } else {
                                // Default layout: debtor on left, creditor on right
                                Triple(debtorId, creditorId, null)
                            }

                            Surface(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(vertical = 2.dp),
                                shape = MaterialTheme.shapes.medium,
                            ) {
                                Row(
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .padding(16.dp),
                                    horizontalArrangement = Arrangement.SpaceBetween,
                                    verticalAlignment = Alignment.CenterVertically
                                ) {
                                    // Determine if the filtered member is receiving or paying
                                    val filteredMemberIsCreditor = creditorSelected && !debtorSelected
                                    val filteredMemberIsDebtor = debtorSelected && !creditorSelected

                                    // Left side - Member (filtered member when single filter applied, otherwise debtor)
                                    Row(
                                        verticalAlignment = Alignment.CenterVertically,
                                        modifier = Modifier.weight(1f)
                                    ) {
                                        // Left member avatar
                                        val leftMemberAvatar = getMemberAvatar(leftMemberId)
                                        if (leftMemberAvatar != null) {
                                            Text(
                                                text = leftMemberAvatar,
                                                style = MaterialTheme.typography.displaySmall,
                                                modifier = Modifier.padding(end = 4.dp)
                                            )
                                        } else {
                                            Icon(
                                                painter = painterResource(id = R.drawable.account_outline),
                                                contentDescription = null,
                                                modifier = Modifier.size(38.dp).padding(end = 2.dp),
                                                tint = MaterialTheme.colorScheme.onSurfaceVariant
                                            )
                                        }

                                        // Animate the left member name color
                                        val leftMemberSelected = leftMemberId in uiState.selectedUserFilters
                                        val leftMemberTextColor = animateColorAsState(
                                            targetValue = if (leftMemberSelected)
                                                MaterialTheme.colorScheme.primary
                                            else
                                                MaterialTheme.colorScheme.onSurface,
                                            animationSpec = tween(
                                                durationMillis = 300,
                                                easing = FastOutSlowInEasing
                                            ),
                                            label = "leftMemberTextColor"
                                        )

                                        Text(
                                            text = leftMemberId,
                                            style = MaterialTheme.typography.titleMedium,
                                            color = leftMemberTextColor.value,
                                            fontWeight = if (leftMemberSelected) FontWeight.Bold else FontWeight.Normal
                                        )
                                    }

                                    // Center - Arrow and Amount
                                    Column(
                                        horizontalAlignment = Alignment.CenterHorizontally,
                                        modifier = Modifier.padding(horizontal = 8.dp)
                                    ) {
                                        // Determine arrow direction and color based on filtered member
                                        // Arrow direction represents cashflow from filtered member's perspective:
                                        // - RIGHT arrow: money flows OUT from filtered member (negative cashflow)
                                        // - LEFT arrow: money flows IN to filtered member (positive cashflow)
                                        val (arrowDirection, arrowColorTarget) = when {
                                            singleMemberFilter && filteredMemberOwes == true -> {
                                                // Filtered member owes money: arrow points RIGHT (money flows OUT)
                                                // Visual: FilteredMember → OtherMember (red arrow)
                                                Pair(R.drawable.arrow_right_thin, MaterialTheme.colorScheme.error)
                                            }
                                            singleMemberFilter && filteredMemberOwes == false -> {
                                                // Filtered member is owed money: arrow points LEFT (money flows IN)
                                                // Visual: FilteredMember ← OtherMember (green arrow)
                                                Pair(R.drawable.arrow_left_thin, Color(160,245,140))
                                            }
                                            filteredMemberIsCreditor -> {
                                                // Legacy logic: filtered member is creditor
                                                Pair(R.drawable.arrow_right_thin, Color(160,245,140))
                                            }
                                            filteredMemberIsDebtor -> {
                                                // Legacy logic: filtered member is debtor
                                                Pair(R.drawable.arrow_right_thin, MaterialTheme.colorScheme.error)
                                            }
                                            else -> {
                                                // Default: arrow points right
                                                Pair(R.drawable.arrow_right_thin, MaterialTheme.colorScheme.onSurfaceVariant)
                                            }
                                        }

                                        val arrowColor = animateColorAsState(
                                            targetValue = arrowColorTarget,
                                            animationSpec = tween(
                                                durationMillis = 300,
                                                easing = FastOutSlowInEasing
                                            ),
                                            label = "arrowColor"
                                        )

                                        Icon(
                                            painter = painterResource(id = arrowDirection),
                                            contentDescription = "Cash flow direction",
                                            tint = arrowColor.value,
                                            modifier = Modifier.size(32.dp)
                                        )

                                        // Animate the settlement amount
                                        val animatedAmount = animateFloatAsState(
                                            targetValue = amount.toFloat(),
                                            animationSpec = tween(
                                                durationMillis = 800,
                                                easing = FastOutSlowInEasing
                                            ),
                                            label = "settlementAmount"
                                        )

                                        Text(
                                            text = "${String.format("%.2f", animatedAmount.value)}€",
                                            style = MaterialTheme.typography.titleMedium,
                                            color = arrowColor.value,
                                            fontWeight = FontWeight.Bold
                                        )
                                    }

                                    // Right side - Member (other member when single filter applied, otherwise creditor)
                                    Row(
                                        verticalAlignment = Alignment.CenterVertically,
                                        horizontalArrangement = Arrangement.End,
                                        modifier = Modifier.weight(1f)
                                    ) {
                                        // Animate the right member name color
                                        val rightMemberSelected = rightMemberId in uiState.selectedUserFilters
                                        val rightMemberTextColor = animateColorAsState(
                                            targetValue = if (rightMemberSelected)
                                                MaterialTheme.colorScheme.primary
                                            else
                                                MaterialTheme.colorScheme.onSurface,
                                            animationSpec = tween(
                                                durationMillis = 300,
                                                easing = FastOutSlowInEasing
                                            ),
                                            label = "rightMemberTextColor"
                                        )

                                        Text(
                                            text = rightMemberId,
                                            style = MaterialTheme.typography.titleMedium,
                                            color = rightMemberTextColor.value,
                                            fontWeight = if (rightMemberSelected) FontWeight.Bold else FontWeight.Normal
                                        )

                                        // Right member avatar
                                        val rightMemberAvatar = getMemberAvatar(rightMemberId)
                                        if (rightMemberAvatar != null) {
                                            Text(
                                                text = rightMemberAvatar,
                                                style = MaterialTheme.typography.displaySmall,
                                                modifier = Modifier.padding(start = 4.dp)
                                            )
                                        } else {
                                            Icon(
                                                painter = painterResource(id = R.drawable.account_outline),
                                                contentDescription = null,
                                                modifier = Modifier.size(38.dp).padding(start = 2.dp),
                                                tint = MaterialTheme.colorScheme.onSurfaceVariant
                                            )
                                        }
                                    }
                                }
                            }
                            if (index < filteredSettlements.lastIndex) {
                                HorizontalDivider(
                                    modifier = Modifier,
                                    thickness = 1.dp,
                                    color = MaterialTheme.colorScheme.secondaryContainer
                                )
                            }
                        }
                    }
                }
            }

            // Add some bottom padding to ensure last items are fully visible
            Spacer(modifier = Modifier.height(16.dp))
        }

        // Bottom navigation arrows - positioned at the absolute bottom of the screen
        if (onPreviousClick != null || onNextClick != null) {
            Surface(
                modifier = Modifier
                    .align(Alignment.BottomCenter)
                    .fillMaxWidth(),
                color = MaterialTheme.colorScheme.surface,
                shadowElevation = 8.dp
            ) {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 16.dp, horizontal = 32.dp),
                    horizontalArrangement = Arrangement.SpaceEvenly
                ) {
                    IconButton(
                        onClick = onPreviousClick ?: { },
                        enabled = hasPrevious && onPreviousClick != null
                    ) {
                        Icon(
                            painter = painterResource(id = R.drawable.chevron_left),
                            contentDescription = "Previous",
                            tint = if (hasPrevious && onPreviousClick != null)
                                MaterialTheme.colorScheme.primary
                            else
                                MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.3f)
                        )
                    }

                    IconButton(
                        onClick = onNextClick ?: { },
                        enabled = hasNext && onNextClick != null
                    ) {
                        Icon(
                            painter = painterResource(id = R.drawable.chevron_right),
                            contentDescription = "Next",
                            tint = if (hasNext && onNextClick != null)
                                MaterialTheme.colorScheme.primary
                            else
                                MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.3f)
                        )
                    }
                }
            }
        }
    }
}
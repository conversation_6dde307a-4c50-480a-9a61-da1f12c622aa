package com.example.splitexpenses.ui.components

import android.annotation.SuppressLint
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.animateColorAsState
import androidx.compose.animation.animateContentSize
import androidx.compose.animation.core.FastOutSlowInEasing
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.tween
import androidx.compose.animation.expandVertically
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.shrinkVertically
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.combinedClickable
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.itemsIndexed
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.filled.Clear
import androidx.compose.material.icons.filled.DateRange
import androidx.compose.material.icons.filled.Delete
import androidx.compose.material3.AlertDialog
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Checkbox
import androidx.compose.material3.CheckboxDefaults
import androidx.compose.material3.DatePicker
import androidx.compose.material3.DatePickerDialog
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.ExposedDropdownMenuBox
import androidx.compose.material3.ExposedDropdownMenuDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.material3.rememberDatePickerState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableLongStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.focus.onFocusChanged
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import com.example.splitexpenses.data.Expense
import com.example.splitexpenses.data.GroupData
import com.example.splitexpenses.ui.components.OfflineStatusIndicator
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale
import androidx.compose.material3.DropdownMenuItem
import androidx.compose.material3.MenuAnchorType.Companion.PrimaryNotEditable
import androidx.compose.material3.TextFieldDefaults
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import com.example.splitexpenses.R

@SuppressLint("DefaultLocale")
@OptIn(
    ExperimentalMaterial3Api::class,
    androidx.compose.foundation.ExperimentalFoundationApi::class
)
@Composable
fun ExpenseEditScreen(
    expense: Expense?,
    group: GroupData,
    currentUser: String,
    onSave: (amount: Double, description: String, paidBy: String, splitBetween: List<String>, category: String, date: Long, isCategoryLocked: Boolean) -> Unit,
    onCancel: () -> Unit,
    onDelete: (() -> Unit)? = null,
    isOffline: Boolean = false
) {
    // Function to get member avatar
    fun getMemberAvatar(memberName: String): String? {
        return group.memberAvatars[memberName]
    }
    // Determine if this is a new expense or an existing one
    val isNewExpense = expense?.id?.isEmpty() ?: true
    println("ExpenseEditScreen: expense=$expense, expense?.id=${expense?.id}, isNewExpense=$isNewExpense")

    // Check if editing should be disabled (offline and existing expense)
    val isEditingDisabled = isOffline && !isNewExpense
    var amount by remember {
        mutableStateOf(
            if (expense?.amount == 0.0) "0" else expense?.amount?.toString() ?: "0"
        )
    }
    var isAmountFocused by remember { mutableStateOf(false) }
    var description by remember { mutableStateOf(expense?.description ?: "") }
    var paidBy by remember {
        mutableStateOf(expense?.paidBy?.ifEmpty { currentUser } ?: currentUser)
    }
    var splitBetween by remember {
        mutableStateOf(expense?.splitBetween?.ifEmpty { group.members } ?: group.members)
    }
    var showPaidByMenu by remember { mutableStateOf(false) }
    var category by remember { mutableStateOf(expense?.category?.ifEmpty { "None" } ?: "None") }
    var showCategoryMenu by remember { mutableStateOf(false) }
    var date by remember { mutableLongStateOf(expense?.date ?: System.currentTimeMillis()) }
    var showDatePicker by remember { mutableStateOf(false) }
    // Initialize with a default value
    var isCategoryLocked by remember { mutableStateOf(expense?.isCategoryLocked ?: false) }

    // Set the initial lock state when the screen is first displayed
    LaunchedEffect(expense) {
        var initialLockState = false
        if (!isNewExpense) {
            // For existing expenses, use the stored value or default to locked (true)
            initialLockState = true
        }
        isCategoryLocked = initialLockState
    }

    // Auto-detect category when description changes, but only if category is not locked
    LaunchedEffect(description) {
        println("ExpenseEditScreen: LaunchedEffect triggered, description=$description, isCategoryLocked=$isCategoryLocked")
        if (description.isNotEmpty() && !isCategoryLocked) {
            val detectedCategory =
                com.example.splitexpenses.data.detectCategory(description, group.categories)
            println("ExpenseEditScreen: Detected category=$detectedCategory")
            if (detectedCategory != "None") {
                category = detectedCategory
                println("ExpenseEditScreen: Category updated to $detectedCategory")
            }
        } else {
            println("ExpenseEditScreen: Category detection skipped, isCategoryLocked=$isCategoryLocked")
        }
    }

    val dateFormat = remember { SimpleDateFormat("dd/MM/yy", Locale.getDefault()) }

    Box(modifier = Modifier.fillMaxSize()) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp)
        ) {
            // Header
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                IconButton(onClick = onCancel) {
                    Icon(Icons.AutoMirrored.Filled.ArrowBack, contentDescription = "Back")
                }
                Text(
                    text = if (isNewExpense) "Add Expense" else "Edit Expense",
                    style = MaterialTheme.typography.headlineLarge,
                    color = MaterialTheme.colorScheme.primary
                )
                // Delete button for existing expenses, empty spacer for new expenses
                if (!isNewExpense && onDelete != null) {
                    IconButton(onClick = onDelete) {
                        Icon(
                            Icons.Default.Delete,
                            contentDescription = "Delete expense",
                            tint = MaterialTheme.colorScheme.error
                        )
                    }
                } else {
                    Spacer(modifier = Modifier.width(48.dp))
                }
            }

            // Show offline status indicator if offline and editing existing expense
            if (isEditingDisabled) {
                OfflineStatusIndicator(
                    isOffline = true,
                    modifier = Modifier.padding(vertical = 8.dp)
                )
            }

            Spacer(modifier = Modifier.height(16.dp))

            // Main content in a Surface
            Surface(
                modifier = Modifier
                    .weight(1f)
                    .fillMaxWidth(),
                shape = MaterialTheme.shapes.medium,
                color = MaterialTheme.colorScheme.surface,

                ) {
                Column(
                    modifier = Modifier
                        .fillMaxSize()
                        .verticalScroll(rememberScrollState())
                        .padding(16.dp)
                ) {
                    // Amount and Category row
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.spacedBy(16.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        OutlinedTextField(
                            value = if (isAmountFocused && amount == "0") "" else amount,
                            onValueChange = {
                                if (it.isEmpty()) {
                                    amount = it
                                } else {
                                    val value = it.toDoubleOrNull()
                                    if (value != null && value <= 99999.99) {
                                        amount = it
                                    }
                                }
                            },
                            label = { Text("Amount") },
                            keyboardOptions = KeyboardOptions(
                                keyboardType = KeyboardType.Decimal,
                                imeAction = ImeAction.Next
                            ),
                            singleLine = true,
                            enabled = !isEditingDisabled,
                            modifier = Modifier
                                .weight(1f)
                                .onFocusChanged { isAmountFocused = it.isFocused },
                            colors = TextFieldDefaults.colors(
                                unfocusedContainerColor = Color.Transparent,
                                focusedContainerColor = Color.Transparent,
                                unfocusedIndicatorColor = MaterialTheme.colorScheme.secondaryContainer
                            )
                        )

                        Column(
                            horizontalAlignment = Alignment.CenterHorizontally
                        ) {
                            // Animate the category lock state
                            val borderWidth = animateFloatAsState(
                                targetValue = if (isCategoryLocked) 2f else 1f,
                                animationSpec = tween(
                                    durationMillis = 300,
                                    easing = FastOutSlowInEasing
                                ),
                                label = "categoryBorderWidth"
                            )

                            val borderColor = animateColorAsState(
                                targetValue = if (isCategoryLocked)
                                    MaterialTheme.colorScheme.primary
                                else
                                    MaterialTheme.colorScheme.secondaryContainer,
                                animationSpec = tween(
                                    durationMillis = 300,
                                    easing = FastOutSlowInEasing
                                ),
                                label = "categoryBorderColor"
                            )

                            Box {
                                Surface(
                                    modifier = Modifier
                                        .width(56.dp)
                                        .height(64.dp)
                                        .padding(top = 8.dp)
                                        .combinedClickable(
                                            onClick = {
                                                if (!isEditingDisabled) {
                                                    showCategoryMenu = true
                                                }
                                            },
                                            onLongClick = {
                                                if (!isEditingDisabled) {
                                                    isCategoryLocked = !isCategoryLocked
                                                }
                                            }
                                        ),
                                    shape = MaterialTheme.shapes.extraSmall,
                                    border = BorderStroke(
                                        width = borderWidth.value.dp,
                                        color = borderColor.value
                                    ),
                                    color = MaterialTheme.colorScheme.surface
                                ) {
                                    // Category emoji in center
                                    Box(
                                        modifier = Modifier
                                            .fillMaxSize()
                                            .padding(vertical = 4.dp),
                                        contentAlignment = Alignment.Center
                                    ) {
                                        Text(
                                            text = group.categories.find { cat -> cat.name == category }?.emoji
                                                ?: "💰",
                                            style = MaterialTheme.typography.titleLarge
                                        )
                                    }
                                }

                                // Lock badge on the border (top-right corner)
                                Box(
                                    modifier = Modifier
                                        .align(Alignment.TopEnd)
                                        .size(18.dp)
                                        .background(
                                            color = MaterialTheme.colorScheme.surface,
                                            shape = CircleShape
                                        ),
                                    contentAlignment = Alignment.Center
                                ) {
                                    Icon(
                                        painter = painterResource(
                                            id = if (isCategoryLocked) R.drawable.lock else R.drawable.lock_open_variant
                                        ),
                                        contentDescription = if (isCategoryLocked) "Category locked" else "Category unlocked",
                                        modifier = Modifier.size(12.dp),
                                        tint = if (isCategoryLocked)
                                            MaterialTheme.colorScheme.primary
                                        else
                                            MaterialTheme.colorScheme.onSurfaceVariant
                                    )
                                }
                            }
                        }
                    }

                    Spacer(modifier = Modifier.height(16.dp))

                    // Description
                    OutlinedTextField(
                        value = description,
                        onValueChange = { description = it },
                        label = { Text("Description") },
                        modifier = Modifier.fillMaxWidth(),
                        singleLine = true,
                        enabled = !isEditingDisabled,
                        colors = TextFieldDefaults.colors(
                            unfocusedContainerColor = Color.Transparent,
                            focusedContainerColor = Color.Transparent,
                            unfocusedIndicatorColor = MaterialTheme.colorScheme.secondaryContainer
                        )
                    )

                    Spacer(modifier = Modifier.height(16.dp))

                    // Paid by and Date row
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.spacedBy(16.dp)
                    ) {
                        // Paid by
                        ExposedDropdownMenuBox(
                            expanded = showPaidByMenu && !isEditingDisabled,
                            onExpandedChange = {
                                if (!isEditingDisabled) {
                                    showPaidByMenu = it
                                }
                            },
                            modifier = Modifier.weight(1f)
                        ) {
                            OutlinedTextField(
                                value = paidBy,
                                onValueChange = {},
                                readOnly = true,
                                label = { Text("Paid by") },
                                trailingIcon = { ExposedDropdownMenuDefaults.TrailingIcon(expanded = showPaidByMenu) },
                                modifier = Modifier
                                    .menuAnchor(PrimaryNotEditable, true)
                                    .fillMaxWidth(),
                                //.exposedDropdownSize(),
                                singleLine = true,
                                enabled = !isEditingDisabled,
                                colors = TextFieldDefaults.colors(
                                    unfocusedContainerColor = Color.Transparent,
                                    focusedContainerColor = Color.Transparent,
                                    unfocusedIndicatorColor = MaterialTheme.colorScheme.secondaryContainer
                                )
                            )

                            ExposedDropdownMenu(
                                expanded = showPaidByMenu,
                                onDismissRequest = { showPaidByMenu = false },
                                containerColor = MaterialTheme.colorScheme.surface,
                                tonalElevation = 8.dp,
                            ) {
                                group.members.forEach { member ->
                                    DropdownMenuItem(
                                        text = {
                                            Row(verticalAlignment = Alignment.CenterVertically) {
                                                // Avatar for member
                                                val memberAvatar = getMemberAvatar(member)
                                                if (memberAvatar != null) {
                                                    Text(
                                                        text = memberAvatar,
                                                        style = MaterialTheme.typography.bodyLarge,
                                                        modifier = Modifier.padding(end = 4.dp)
                                                    )
                                                } else {
                                                    Icon(
                                                        painter = painterResource(id = R.drawable.account_outline),
                                                        contentDescription = null,
                                                        modifier = Modifier.size(20.dp),
                                                        tint = MaterialTheme.colorScheme.onSurfaceVariant
                                                    )
                                                }
                                                Spacer(modifier = Modifier.width(8.dp))
                                                Text(member)
                                            }
                                        },
                                        onClick = {
                                            paidBy = member
                                            showPaidByMenu = false
                                        }
                                    )
                                }
                            }
                        }

                        // Date
                        OutlinedTextField(
                            value = dateFormat.format(Date(date)),
                            onValueChange = { },
                            readOnly = true,
                            label = { Text("Date") },
                            trailingIcon = {
                                IconButton(
                                    onClick = {
                                        if (!isEditingDisabled) {
                                            showDatePicker = true
                                        }
                                    },
                                    enabled = !isEditingDisabled
                                ) {
                                    Icon(
                                        Icons.Default.DateRange,
                                        contentDescription = "Select date"
                                    )
                                }
                            },
                            modifier = Modifier.weight(1f),
                            singleLine = true,
                            enabled = !isEditingDisabled,
                            colors = TextFieldDefaults.colors(
                                unfocusedContainerColor = Color.Transparent,
                                focusedContainerColor = Color.Transparent,
                                unfocusedIndicatorColor = MaterialTheme.colorScheme.secondaryContainer
                            )
                        )
                    }


                    Spacer(modifier = Modifier.height(16.dp))

                    // Date picker dialog
                    if (showDatePicker) {
                        val datePickerState = rememberDatePickerState(
                            initialSelectedDateMillis = date
                        )
                        DatePickerDialog(
                            onDismissRequest = { showDatePicker = false },
                            confirmButton = {
                                TextButton(
                                    onClick = {
                                        datePickerState.selectedDateMillis?.let {
                                            date = it
                                        }
                                        showDatePicker = false
                                    }
                                ) {
                                    Text("OK")
                                }
                            },
                            dismissButton = {
                                TextButton(
                                    onClick = { showDatePicker = false }
                                ) {
                                    Text("Cancel")
                                }
                            }
                        ) {
                            DatePicker(state = datePickerState)
                        }
                    }

                    Spacer(modifier = Modifier.height(16.dp))

                    // Split between
                    Row(
                        modifier = Modifier.fillMaxWidth(),
//                        horizontalArrangement = Arrangement.SpaceBetween,
                        horizontalArrangement = Arrangement.spacedBy(8.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Row(
                            horizontalArrangement = Arrangement.spacedBy(8.dp)
                        ) {
                            Icon(
                                painter = painterResource(id = R.drawable.account_group_outline),
                                contentDescription = null,
                                tint = MaterialTheme.colorScheme.primary,
                                modifier = Modifier.size(20.dp)
                            )
                            Text(
                                text = "Split between",
                                style = MaterialTheme.typography.titleMedium,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                        Spacer(modifier = Modifier.weight(1f))
                        Row(
                            verticalAlignment = Alignment.CenterVertically,
                            horizontalArrangement = Arrangement.spacedBy(8.dp)
                        ) {
                            TextButton(
                                onClick = {
                                    if (!isEditingDisabled) {
                                        splitBetween =
                                            if (splitBetween.size == group.members.size) {
                                                emptyList()
                                            } else {
                                                group.members
                                            }
                                    }
                                },
                                enabled = !isEditingDisabled
                            ) {
                                Text(if (splitBetween.size == group.members.size) "None" else "All")
                            }
                        }
                    }
                    Spacer(modifier = Modifier.height(8.dp))

                    // Two-column member list
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.spacedBy(16.dp)
                    ) {
                        // First column
                        Column(
                            modifier = Modifier.weight(1f),
                            verticalArrangement = Arrangement.spacedBy(4.dp)
                        ) {
                            val halfSize = (group.members.size + 1) / 2
                            group.members.take(halfSize).forEach { member ->
                                Row(
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .padding(vertical = 4.dp)
                                        .clickable(enabled = !isEditingDisabled) {
                                            if (!isEditingDisabled) {
                                                splitBetween = if (member in splitBetween) {
                                                    splitBetween - member
                                                } else {
                                                    splitBetween + member
                                                }
                                            }
                                        },
                                    horizontalArrangement = Arrangement.SpaceBetween,
                                    verticalAlignment = Alignment.CenterVertically
                                ) {
                                    Row(
                                        verticalAlignment = Alignment.CenterVertically,
                                        horizontalArrangement = Arrangement.spacedBy(4.dp),
                                        modifier = Modifier.weight(1f)
                                    ) {
                                        // Avatar for member
                                        val memberAvatar = getMemberAvatar(member)
                                        if (memberAvatar != null) {
                                            Text(
                                                text = memberAvatar,
                                                style = MaterialTheme.typography.bodyLarge,
                                                color = if (member in splitBetween)
                                                    MaterialTheme.colorScheme.primary
                                                else
                                                    MaterialTheme.colorScheme.onSurfaceVariant
                                            )
                                        } else {
                                            Icon(
                                                painter = painterResource(id = R.drawable.account_outline),
                                                contentDescription = null,
                                                modifier = Modifier.size(20.dp),
                                                tint = if (member in splitBetween)
                                                    MaterialTheme.colorScheme.primary
                                                else
                                                    MaterialTheme.colorScheme.surfaceVariant
                                            )
                                        }
                                        // Animate the member name color
                                        val isSelected = member in splitBetween
                                        val textColor = animateColorAsState(
                                            targetValue = if (isSelected)
                                                MaterialTheme.colorScheme.primary
                                            else
                                                MaterialTheme.colorScheme.onSurfaceVariant,
                                            animationSpec = tween(
                                                durationMillis = 300,
                                                easing = FastOutSlowInEasing
                                            ),
                                            label = "memberTextColor_$member"
                                        )

                                        Text(
                                            text = member,
                                            modifier = Modifier.weight(1f),
                                            color = textColor.value
                                        )
                                    }
                                    // Animate the per-person amount appearance
                                    AnimatedVisibility(
                                        visible = member in splitBetween,
                                        enter = fadeIn(animationSpec = tween(300)) + expandVertically(
                                            animationSpec = tween(300, easing = FastOutSlowInEasing)
                                        ),
                                        exit = fadeOut(animationSpec = tween(300)) + shrinkVertically(
                                            animationSpec = tween(300, easing = FastOutSlowInEasing)
                                        )
                                    ) {
                                        // Animate the amount value with safety check for division by zero
                                        val perPersonAmount = if (splitBetween.isNotEmpty()) {
                                            (amount.toDoubleOrNull() ?: 0.0) / splitBetween.size
                                        } else {
                                            0.0
                                        }
                                        val animatedAmount = animateFloatAsState(
                                            targetValue = perPersonAmount.toFloat(),
                                            animationSpec = tween(
                                                durationMillis = 800,
                                                easing = FastOutSlowInEasing
                                            ),
                                            label = "perPersonAmount_$member"
                                        )

                                        Text(
                                            text = "${String.format("%.2f", animatedAmount.value)}€",
                                            style = MaterialTheme.typography.bodyLarge,
                                            color = MaterialTheme.colorScheme.tertiary
                                        )
                                    }
                                }
                            }
                        }

                        // Second column
                        Column(
                            modifier = Modifier.weight(1f),
                            verticalArrangement = Arrangement.spacedBy(4.dp)
                        ) {
                            val halfSize = (group.members.size + 1) / 2
                            group.members.drop(halfSize).forEach { member ->
                                Row(
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .padding(vertical = 4.dp)
                                        .clickable(enabled = !isEditingDisabled) {
                                            if (!isEditingDisabled) {
                                                splitBetween = if (member in splitBetween) {
                                                    splitBetween - member
                                                } else {
                                                    splitBetween + member
                                                }
                                            }
                                        },
                                    horizontalArrangement = Arrangement.SpaceBetween,
                                    verticalAlignment = Alignment.CenterVertically
                                ) {
                                    Row(
                                        verticalAlignment = Alignment.CenterVertically,
                                        horizontalArrangement = Arrangement.spacedBy(4.dp),
                                        modifier = Modifier.weight(1f)
                                    ) {
                                        // Avatar for member
                                        val memberAvatar = getMemberAvatar(member)
                                        if (memberAvatar != null) {
                                            Text(
                                                text = memberAvatar,
                                                style = MaterialTheme.typography.bodyLarge,
                                                color = if (member in splitBetween)
                                                    MaterialTheme.colorScheme.primary
                                                else
                                                    MaterialTheme.colorScheme.onSurfaceVariant
                                            )
                                        } else {
                                            Icon(
                                                painter = painterResource(id = R.drawable.account_outline),
                                                contentDescription = null,
                                                modifier = Modifier.size(20.dp),
                                                tint = if (member in splitBetween)
                                                    MaterialTheme.colorScheme.primary
                                                else
                                                    MaterialTheme.colorScheme.surfaceVariant
                                            )
                                        }
                                        // Animate the member name color
                                        val isSelected = member in splitBetween
                                        val textColor = animateColorAsState(
                                            targetValue = if (isSelected)
                                                MaterialTheme.colorScheme.primary
                                            else
                                                MaterialTheme.colorScheme.onSurfaceVariant,
                                            animationSpec = tween(
                                                durationMillis = 300,
                                                easing = FastOutSlowInEasing
                                            ),
                                            label = "memberTextColor_$member"
                                        )

                                        Text(
                                            text = member,
                                            modifier = Modifier.weight(1f),
                                            color = textColor.value
                                        )
                                    }
                                    // Animate the per-person amount appearance
                                    AnimatedVisibility(
                                        visible = member in splitBetween,
                                        enter = fadeIn(animationSpec = tween(300)) + expandVertically(
                                            animationSpec = tween(300, easing = FastOutSlowInEasing)
                                        ),
                                        exit = fadeOut(animationSpec = tween(300)) + shrinkVertically(
                                            animationSpec = tween(300, easing = FastOutSlowInEasing)
                                        )
                                    ) {
                                        // Animate the amount value with safety check for division by zero
                                        val perPersonAmount = if (splitBetween.isNotEmpty()) {
                                            (amount.toDoubleOrNull() ?: 0.0) / splitBetween.size
                                        } else {
                                            0.0
                                        }
                                        val animatedAmount = animateFloatAsState(
                                            targetValue = perPersonAmount.toFloat(),
                                            animationSpec = tween(
                                                durationMillis = 800,
                                                easing = FastOutSlowInEasing
                                            ),
                                            label = "perPersonAmount_$member"
                                        )

                                        Text(
                                            text = "${
                                                String.format("%.2f", animatedAmount.value)}€",
                                            style = MaterialTheme.typography.bodyLarge,
                                            color = MaterialTheme.colorScheme.tertiary
                                        )
                                    }
                                }
                            }
                        }
                    }
                }
            }

            Spacer(modifier = Modifier.height(16.dp))

            // Save button with animation
            val isFormValid =
                amount.isNotEmpty() && paidBy.isNotEmpty() && splitBetween.isNotEmpty() && category.isNotEmpty()
            val isSaveEnabled = isFormValid && !isEditingDisabled

            // Animate the button color
            val buttonColor = animateColorAsState(
                targetValue = if (isSaveEnabled)
                    MaterialTheme.colorScheme.primary
                else
                    MaterialTheme.colorScheme.surfaceVariant,
                animationSpec = tween(
                    durationMillis = 300,
                    easing = FastOutSlowInEasing
                ),
                label = "saveButtonColor"
            )

            // Animate the button text color
            val buttonTextColor = animateColorAsState(
                targetValue = if (isSaveEnabled)
                    MaterialTheme.colorScheme.onPrimary
                else
                    MaterialTheme.colorScheme.onSurfaceVariant,
                animationSpec = tween(
                    durationMillis = 300,
                    easing = FastOutSlowInEasing
                ),
                label = "saveButtonTextColor"
            )

            Button(
                onClick = {
                    onSave(
                        amount.toDoubleOrNull() ?: 0.0,
                        description,
                        paidBy,
                        splitBetween,
                        category,
                        date,
                        isCategoryLocked
                    )
                },
                enabled = isSaveEnabled,
                modifier = Modifier.fillMaxWidth(),
                colors = ButtonDefaults.buttonColors(
                    containerColor = buttonColor.value,
                    contentColor = buttonTextColor.value,
                    disabledContainerColor = MaterialTheme.colorScheme.surfaceVariant,
                    disabledContentColor = MaterialTheme.colorScheme.onSurfaceVariant
                )
            ) {
                Text(if (isNewExpense) "Add" else "Save")
            }
        }

        // Category selection dialog
        if (showCategoryMenu) {
            AlertDialog(
                onDismissRequest = { showCategoryMenu = false },
                containerColor = MaterialTheme.colorScheme.surface,
                tonalElevation = 8.dp,
                title = {
                    Text(
                        text = "Select Category",
                        style = MaterialTheme.typography.headlineSmall,
                        color = MaterialTheme.colorScheme.primary,
                        modifier = Modifier.animateContentSize(
                            animationSpec = tween(
                                durationMillis = 300,
                                easing = FastOutSlowInEasing
                            )
                        )
                    )
                },
                text = {
                    Column(
                        modifier = Modifier
                            .fillMaxWidth()
                    ) {
                        // Categories grid with animation
                        LazyVerticalGrid(
                            columns = GridCells.Fixed(2),
                            modifier = Modifier
                                .fillMaxWidth()
                                .heightIn(max = 300.dp),
                            contentPadding = PaddingValues(vertical = 8.dp),
                            verticalArrangement = Arrangement.spacedBy(8.dp),
                            horizontalArrangement = Arrangement.spacedBy(8.dp)
                        ) {
                            itemsIndexed(group.categories) { index, cat ->

                                // Highlight the currently selected category
                                val isSelected = category == cat.name
                                val backgroundColor = animateColorAsState(
                                    targetValue = if (isSelected)
                                        MaterialTheme.colorScheme.primaryContainer
                                    else
                                        Color.Transparent,
                                    animationSpec = tween(
                                        durationMillis = 300,
                                        easing = FastOutSlowInEasing
                                    ),
                                    label = "categoryBackground"
                                )

                                Surface(
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .clickable {
                                            category = cat.name
                                            showCategoryMenu = false
                                        },
                                    shape = MaterialTheme.shapes.medium,
                                    color = backgroundColor.value,
                                    border = if (isSelected)
                                        BorderStroke(2.dp, MaterialTheme.colorScheme.primary)
                                    else
                                        BorderStroke(
                                            1.dp,
                                            MaterialTheme.colorScheme.secondaryContainer
                                        )
                                ) {
                                    Column(
                                        modifier = Modifier
                                            .padding(12.dp),
                                        horizontalAlignment = Alignment.CenterHorizontally
                                    ) {
                                        Text(
                                            text = cat.emoji,
                                            style = MaterialTheme.typography.headlineMedium
                                        )
                                        Spacer(modifier = Modifier.height(4.dp))
                                        Text(
                                            text = cat.name,
                                            style = MaterialTheme.typography.bodyMedium,
                                            textAlign = TextAlign.Center,
                                            maxLines = 1,
                                            overflow = TextOverflow.Ellipsis
                                        )
                                    }
                                }
                            }
                        }
                    }
                },
                confirmButton = {
                    TextButton(onClick = { showCategoryMenu = false }) {
                        Text("Cancel")
                    }
                }
            )
        }
    }
}